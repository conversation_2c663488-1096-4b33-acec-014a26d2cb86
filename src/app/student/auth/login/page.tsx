'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '../../../../components/auth/AuthProvider';
import { useEffect } from 'react';

export default function StudentLogin() {
  const router = useRouter();
  const { user, userRole, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && user) {
      if (userRole === 'student') {
        router.push('/student-dashboard');
      } else {
        // Non-student users redirect to main site
        window.location.href = 'https://languagegems.com/account';
      }
    }
  }, [user, userRole, isLoading, router]);

  // Redirect to main site auth with student context
  useEffect(() => {
    const currentUrl = window.location.href;
    const mainSiteLogin = `https://languagegems.com/auth/login?redirectTo=${encodeURIComponent('https://students.languagegems.com/student-dashboard')}`;
    window.location.href = mainSiteLogin;
  }, []);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center text-white">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
        <p className="text-lg">Redirecting to login...</p>
      </div>
    </div>
  );
}
