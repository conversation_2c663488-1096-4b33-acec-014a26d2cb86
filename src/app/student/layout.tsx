import { Inter } from 'next/font/google';
import { AuthProvider } from '../../components/auth/AuthProvider';
import '../../app/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'LanguageGems Student Portal',
  description: 'Master French, Spanish, and German with interactive games and exercises',
};

export default function StudentRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
